# Signatus - Business Logika a Analýza Aplikácie

## Obsah
1. [Úvod a prehľad aplikácie](#úvod-a-prehľad-aplikácie)
2. [Prihlásenie a prvotná analýza](#prihlásenie-a-prvotná-analýza)
3. [Hlavné business procesy](#hlavné-business-procesy)
4. [Používateľské role a oprávnenia](#používateľské-role-a-oprávnenia)
5. [Typy dokumentov a ich spracovanie](#typy-dokumentov-a-ich-spracovanie)
6. [Workflow automatizácia](#workflow-automatizácia)
7. [Business pravidlá a validácie](#business-pravidlá-a-validácie)
8. [Integrácie a závislosti](#integrácie-a-závislosti)
9. [Technické a business obmedzenia](#technické-a-business-obmedzenia)
10. [Z<PERSON>ver a odporúčania](#záver-a-odporúčania)

---

## Úvod a prehľad aplikácie

### Základné informácie
- **Názov aplikácie**: Signatus
- **URL**: https://signatus.ana.sk/app
- **Vývojár**: ANASOFT APR, spol. s r.o.
- **Účel**: Platforma pre elektronické podpisovanie a správu dokumentov

### Kľúčové business hodnoty
- **Digitalizácia**: Prechod z papierových na elektronické dokumenty
- **Efektivita**: Automatizácia workflow procesov
- **Bezpečnosť**: Právne záväzné elektronické podpisy
- **Dostupnosť**: Online aj offline režim práce

---

## Prihlásenie a prvotná analýza

### Prihlasovacie údaje
- **Login**: klimes
- **Heslo**: tomas
- **URL**: https://signatus.ana.sk/app

### Prvotné pozorovania

**Úspešné prihlásenie:**
- Aplikácia je dostupná na URL: https://signatus.ana.sk/app
- Automatické presmerovanie na dashboard: https://signatus.ana.sk/app/#/dashboard
- Používateľ: klimes (prihlásený)
- Aplikácia používa Single Page Application (SPA) architektúru

**Hlavné navigačné sekcie identifikované:**

#### Primárne moduly:
1. **Prehľad** (Dashboard) - aktuálne aktívny
2. **Inbox** - správa prichádzajúcich dokumentov
3. **Proces schvaľovania** - workflow pre schvaľovanie dokumentov
4. **Operation Activities** - operačné aktivity
5. **Registration** - registračné procesy
6. **Leads** - správa potenciálnych klientov
7. **Maintenance** - údržba a servis
8. **Delivery** - doručovanie
9. **Onsite Training** - školenia na mieste
10. **Online Training** - online školenia
11. **Remote Signature** - vzdialené podpisovanie

#### Sekundárne funkcie:
- **Archív** - archivované dokumenty
- **Jazyk** - nastavenie jazyka
- **Zmeniť heslo** - správa hesla
- **Odhlásiť sa** - logout funkcia

**Dashboard prehľad:**
- Možnosť pridania nového procesu
- Filtrovanie podľa časového obdobia (Posledný mesiac)
- Stav obálky s kategóriami: Rozpracované, Pripravené, Prebiehajúce, Dokončené, Zrušené, Expirované
- Všetky procesy momentálne ukazujú 0 záznamov

---

## Hlavné business procesy

### 1. Proces elektronického podpisovania

Na základe analýzy aplikácie som identifikoval hlavný proces elektronického podpisovania:

**Kľúčové zistenia z Remote Signature modulu:**
- Aplikácia spravuje "obálky" (envelope) obsahujúce dokumenty na podpis
- Každá obálka má definované základné údaje: názov, spoločnosť, kontaktnú osobu, telefón
- Systém sleduje 7 rôznych stavov dokumentov
- Existuje časové obmedzenie (exspirácia) pre podpisové procesy
- Aplikácia podporuje export dokončených dokumentov

### 2. Správa dokumentov a workflow stavy

**Identifikované stavy dokumentov:**
1. **Rozpracované** (0) - Dokument v procese tvorby/úpravy
2. **Pripravené** (0) - Dokument pripravený na odoslanie
3. **Prebiehajúce** (0) - Dokument odoslaný, čaká na podpis
4. **Dokončené** (1) - Úspešne podpísané dokumenty
5. **Zrušené** (0) - Zrušené procesy
6. **Exspirované** (2) - Dokumenty s vypršanou platnosťou
7. **Chybové** (0) - Dokumenty s chybami v procese

**Príklad skutočných dát z aplikácie:**
- "Zmluva k poisteniu zahrady" - Dokončený stav (12.3.2025)
- "Skuska OTP kodu" - Exspirovaný stav (18.11.2024)
- "Podpisanie Formularu 1" - Exspirovaný stav (18.11.2024)

### 3. Business moduly a ich účel

**Hlavné business moduly identifikované:**

#### A) Remote Signature
- **Účel**: Vzdialené elektronické podpisovanie
- **Funkcie**: Správa obálok, sledovanie stavov, export dokumentov
- **Dáta**: Názov obálky, spoločnosť, kontakt, telefón, časové značky

#### B) Registration
- **Účel**: Registračné procesy
- **Funkcie**: Správa registrácií s podobnými stavmi ako Remote Signature
- **Dáta**: Celé meno, typ registrujúceho, rodné číslo, labe.city

#### C) Proces schvaľovania
- **Účel**: Workflow pre schvaľovanie dokumentov
- **Funkcie**: Viacstupňové schvaľovacie procesy

#### D) Leads Management
- **Účel**: Správa potenciálnych klientov/obchodných príležitostí

#### E) Operation Activities
- **Účel**: Správa operačných aktivít a procesov

---

## Používateľské role a oprávnenia

**Identifikovaná používateľská rola:**
- **Používateľ**: klimes
- **Typ**: Operačný používateľ s prístupom ku všetkým modulom
- **Oprávnenia**:
  - Zobrazenie všetkých modulov
  - Pridávanie nových procesov
  - Export dokumentov (kde je povolený)
  - Správa vlastného profilu (zmena hesla)
  - Prístup k archívu

**Systémové funkcie dostupné používateľovi:**
- Zmena jazyka rozhrania
- Zmena hesla
- Prístup k archívu dokumentov
- Odhlásenie zo systému

---

## Typy dokumentov a ich spracovanie

**Identifikované typy dokumentov:**

### 1. Remote Signature dokumenty
- **Zmluvy** (napr. "Zmluva k poisteniu zahrady")
- **Formuláre** (napr. "Podpisanie Formularu 1")
- **Testovacie dokumenty** (napr. "Skuska OTP kodu")

### 2. Registration dokumenty
- **Registračné formuláre** s osobnými údajmi
- **Identifikačné dokumenty** (rodné číslo)
- **Lokalizačné údaje** (labe.city)

### 3. Spracovanie dokumentov
**Životný cyklus dokumentu:**
1. **Vytvorenie** - Nový dokument/obálka
2. **Konfigurácia** - Nastavenie podpisových polí a workflow
3. **Odoslanie** - Distribúcia na podpis
4. **Sledovanie** - Monitoring stavu podpisovania
5. **Dokončenie** - Finalizácia po podpise
6. **Archivácia** - Uloženie do archívu

---

## Workflow automatizácia

**Automatizované procesy identifikované:**

### 1. Časové riadenie
- **Exspirácia dokumentov**: Automatické označenie dokumentov ako "Exspirované"
- **Časové značky**: Automatické zaznamenávanie dátumu a času vytvorenia

### 2. Stavové prechody
- Automatické prechody medzi stavmi na základe akcií používateľa
- Validácia stavov pred prechodom do ďalšej fázy

### 3. Notifikačný systém
- Pravdepodobne automatické notifikácie (nie je viditeľné v UI, ale štandardná funkcionalita)

### 4. Export a archivácia
- Automatické sprístupnenie export funkcií pre dokončené dokumenty
- Integrácia s archívnym systémom

---

## Business pravidlá a validácie

**Identifikované business pravidlá:**

### 1. Stavové validácie
- **Povinné polia**: Názov obálky, kontaktné údaje sú povinné pre Remote Signature
- **Stavové prechody**: Dokumenty môžu prechádzať len určitými stavmi v logickom poradí
- **Časové obmedzenia**: Dokumenty majú definovanú dobu platnosti (exspirácia)

### 2. Bezpečnostné pravidlá
- **Autentifikácia**: Povinné prihlásenie pre prístup k aplikácii
- **Oprávnenia**: Rôzne úrovne prístupu k funkciám (napr. Export je niekedy zakázaný)
- **Audit trail**: Sledovanie časových značiek pre všetky akcie

### 3. Dátové validácie
- **Telefónne čísla**: Formát +421XXXXXXXXX pre slovenské čísla
- **Dátumy**: Štandardný formát DD.MM.YYYY, HH:MM
- **Rodné čísla**: Validácia formátu pre Registration modul

### 4. Workflow pravidlá
- **Sekvenčnosť**: Dokumenty musia prejsť definovanými fázami
- **Časové limity**: Automatická exspirácia po určitom čase
- **Notifikácie**: Automatické upozornenia na zmeny stavu

---

## Integrácie a závislosti

**Identifikované integrácie:**

### 1. Externé systémy
- **E-mail systém**: Pre notifikácie a odosielanie pozvárok na podpis
- **SMS brána**: Pre SMS notifikácie (viditeľné z telefónnych čísel)
- **Archívny systém**: Pre dlhodobé uloženie dokumentov

### 2. Interné závislosti
- **Autentifikačný systém**: Centrálna správa používateľov
- **Databázový systém**: Uloženie všetkých business dát
- **File storage**: Uloženie dokumentov a príloh

### 3. API integrácie
- **Export funkcionalita**: Možnosť exportu dát do externých systémov
- **Reporting**: Generovanie reportov a štatistík

---

## Technické a business obmedzenia

**Technické obmedzenia:**

### 1. Používateľské rozhranie
- **Single Page Application**: Všetka navigácia cez hash routing (#/)
- **Responzívnosť**: Optimalizované pre desktop použitie
- **Browser závislosti**: Vyžaduje moderný webový prehliadač

### 2. Výkonnostné obmedzenia
- **Počet záznamov**: Aktuálne nízke počty záznamov (0-3 v moduloch)
- **Concurrent users**: Nie je známe, ale pravdepodobne obmedzené
- **File size limits**: Pravdepodobne existujú limity pre nahrávané dokumenty

**Business obmedzenia:**

### 1. Procesné obmedzenia
- **Časové limity**: Dokumenty expirujú po určitom čase
- **Stavové obmedzenia**: Nemožnosť vrátiť dokument do predchádzajúceho stavu
- **Používateľské oprávnenia**: Obmedzený prístup k určitým funkciám

### 2. Dátové obmedzenia
- **Povinné polia**: Určité polia sú povinné pre vytvorenie procesu
- **Formátové obmedzenia**: Špecifické formáty pre telefóny, dátumy, atď.
- **Kapacitné obmedzenia**: Pravdepodobne limity na počet dokumentov/procesov

---

## Záver a odporúčania

### Kľúčové zistenia

**Silné stránky aplikácie:**
1. **Modulárna architektúra**: Jasne oddelené business moduly
2. **Stavové riadenie**: Dobre definované stavy dokumentov
3. **Audit trail**: Sledovanie časových značiek a stavov
4. **Flexibilita**: Podpora rôznych typov procesov (podpisovanie, registrácia, školenia)

**Oblasti na zlepšenie:**
1. **Používateľské rozhranie**: Niektoré funkcie nereagujú správne (napr. detail view)
2. **Dokumentácia**: Chýba kontextová nápoveda pre používateľov
3. **Reporting**: Obmedzené možnosti reportingu a analytiky
4. **Mobile support**: Optimalizácia pre mobilné zariadenia

### Business hodnota

**Hlavné business benefity:**
- **Digitalizácia**: Eliminácia papierových procesov
- **Efektivita**: Automatizácia workflow procesov
- **Sledovateľnosť**: Kompletný audit trail všetkých akcií
- **Flexibilita**: Podpora rôznych typov business procesov

### Odporúčania pre ďalší rozvoj

1. **UX/UI vylepšenia**: Zlepšenie responzívnosti a používateľskej skúsenosti
2. **Mobile aplikácia**: Vývoj mobilnej verzie pre terénnych pracovníkov
3. **Advanced reporting**: Rozšírenie analytických možností
4. **API rozhranie**: Lepšia integrácia s externými systémami
5. **Workflow designer**: Grafický nástroj na tvorbu vlastných workflow

### Technické odporúčania

1. **Performance monitoring**: Implementácia monitoringu výkonu
2. **Error handling**: Zlepšenie spracovania chýb a používateľských hlásení
3. **Security audit**: Pravidelný bezpečnostný audit
4. **Backup strategy**: Robustná stratégia zálohovania dát

---

## Príloha: Business Entity Model

### Kľúčové business entity a ich atribúty

| Entity | Atribúty | Popis | Modul |
|--------|----------|-------|-------|
| **Remote Signature** | Názov obálky, Názov spoločnosti, Kontaktná osoba, Telefónne číslo, Dátum vytvorenia, Stav | Obálka pre vzdialené podpisovanie dokumentov | Remote Signature |
| **Registration** | Celé meno, Typ registrujúceho, Rodné číslo, labe.city, Dátum vytvorenia, Stav | Registračný záznam osoby/entity | Registration |
| **Process** | Name, Proces, Stav, Dátum vytvorenia, Dátum exspirácie | Všeobecný business proces | Inbox |
| **User** | Login (klimes), Heslo, Jazykové nastavenia | Používateľský účet | Systém |

### Stavový model

| Stav | Popis | Aplikuje sa na |
|------|-------|----------------|
| **Rozpracované** | Proces v tvorbe, nie je dokončený | Všetky moduly |
| **Pripravené** | Proces pripravený na spustenie | Všetky moduly |
| **Prebiehajúce** | Aktívny proces čakajúci na akciu | Všetky moduly |
| **Dokončené** | Úspešne dokončený proces | Všetky moduly |
| **Zrušené** | Manuálne zrušený proces | Všetky moduly |
| **Exspirované** | Proces s vypršanou platnosťou | Všetky moduly |
| **Chybové** | Proces s chybou vyžadujúci zásah | Všetky moduly |

### Business pravidlá matrix

| Pravidlo | Remote Signature | Registration | Ostatné moduly |
|----------|------------------|--------------|----------------|
| **Povinné polia** | Názov obálky, Kontakt | Celé meno, Rodné číslo | Varies |
| **Časová exspirácia** | ✓ | ✓ | ✓ |
| **Stavové prechody** | Sekvenčné | Sekvenčné | Sekvenčné |
| **Export možnosť** | ✓ (podmienečne) | ✗ | Varies |
| **Audit trail** | ✓ | ✓ | ✓ |

---

*Analýza vytvorená: December 2024*
*Analyzovaná verzia: Signatus (aktuálna)*
*Autor analýzy: Business Analyst*
*Metóda analýzy: Priama interakcia s aplikáciou cez MCP Browser Extension*
*Analyzované moduly: Dashboard, Remote Signature, Registration, Inbox, systémové funkcie*
